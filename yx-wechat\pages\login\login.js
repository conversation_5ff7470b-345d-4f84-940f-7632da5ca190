// pages/login/login.js
const auth = require('../../utils/auth.js')
import Toast from '@vant/weapp/toast/toast'

Page({
  data: {
    username: '',
    password: '',
    loading: false
  },

  onLoad() {
    // 如果已经登录，直接跳转到首页
    if (auth.checkLogin()) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 用户名输入
  onUsernameChange(event) {
    this.setData({
      username: event.detail
    })
  },

  // 密码输入
  onPasswordChange(event) {
    this.setData({
      password: event.detail
    })
  },

  // 登录
  onLogin() {
    const { username, password } = this.data

    if (!username.trim()) {
      Toast.fail('请输入用户名')
      return
    }

    if (!password.trim()) {
      Toast.fail('请输入密码')
      return
    }

    this.setData({ loading: true })

    auth.login(username, password)
      .then(() => {
        Toast.success('登录成功')
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
      })
      .catch((err) => {
        console.error('登录失败:', err)
        Toast.fail(err.message || '登录失败')
      })
      .finally(() => {
        this.setData({ loading: false })
      })
  },

  // 跳转到注册页面
  goToRegister() {
    Toast('注册功能暂未开放')
  }
})
