<!--components/question-item/question-item.wxml-->
<view class="question-item">
  <!-- 题目头部 -->
  <view class="question-header">
    <van-tag type="primary" size="mini">{{ getQuestionTypeText(question.type) }}</van-tag>
    <van-tag type="success" size="mini" wx:if="{{ question.difficulty }}">
      {{ getDifficultyText(question.difficulty) }}
    </van-tag>
    <view class="question-score" wx:if="{{ question.score }}">
      {{ question.score }}分
    </view>
  </view>

  <!-- 题目内容 -->
  <view class="question-content">
    {{ question.content }}
  </view>

  <!-- 题目选项 -->
  <view class="question-options" wx:if="{{ question.options && question.options.length > 0 }}">
    <view 
      class="option-item {{ selectedAnswer === item.key ? 'selected' : '' }}"
      wx:for="{{ question.options }}" 
      wx:key="key"
      bind:tap="onOptionSelect"
      data-key="{{ item.key }}"
    >
      <view class="option-label">{{ item.key }}</view>
      <view class="option-content">{{ item.value }}</view>
    </view>
  </view>

  <!-- 答案解析 -->
  <view class="question-analysis" wx:if="{{ showAnalysis }}">
    <van-divider>答案解析</van-divider>
    <view class="correct-answer">
      <text class="label">正确答案：</text>
      <text class="answer">{{ getCorrectAnswerText() }}</text>
    </view>
    <view class="analysis-content" wx:if="{{ question.analysis }}">
      <text class="label">解析：</text>
      <text class="content">{{ question.analysis }}</text>
    </view>
  </view>
</view>
