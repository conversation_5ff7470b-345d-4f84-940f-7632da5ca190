import { api } from '@/api'

const state = {
  token: localStorage.getItem('token') || '',
  userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}'),
  userMenus: JSON.parse(localStorage.getItem('userMenus') || '[]'),
  userPermissions: JSON.parse(localStorage.getItem('userPermissions') || '[]')
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    localStorage.setItem('token', token)
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
  },
  SET_USER_MENUS(state, menus) {
    state.userMenus = menus
    localStorage.setItem('userMenus', JSON.stringify(menus))
  },
  SET_USER_PERMISSIONS(state, permissions) {
    state.userPermissions = permissions
    localStorage.setItem('userPermissions', JSON.stringify(permissions))
  },
  CLEAR_USER_DATA(state) {
    state.token = ''
    state.userInfo = {}
    state.userMenus = []
    state.userPermissions = []
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('userMenus')
    localStorage.removeItem('userPermissions')
  },
  RESET_ROUTES(state) {
    // 重置路由相关状态
    state.userMenus = []
    state.userPermissions = []
  }
}

const actions = {
  // 登录
  async login({ commit, dispatch }, loginData) {
    const response = await api.auth.login(loginData)
    const { token, user } = response.data

    commit('SET_TOKEN', token)
    commit('SET_USER_INFO', user)

    // 获取用户菜单和权限
    await dispatch('getUserMenus')

    return response
  },

  // 获取用户菜单
  async getUserMenus({ commit }) {
    try {
      const response = await api.menus.user()
      const menus = response.data || []
      commit('SET_USER_MENUS', menus)
      return menus
    } catch (error) {
      console.error('获取用户菜单失败:', error)
      return []
    }
  },

  // 获取用户信息
  async getUserInfo({ commit }) {
    const response = await api.auth.profile()
    const userInfo = response.data
    commit('SET_USER_INFO', userInfo)
    return userInfo
  },

  // 登出
  async logout({ commit }) {
    try {
      await api.auth.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      commit('CLEAR_USER_DATA')
      // 重置路由
      commit('permission/RESET_ROUTES', null, { root: true })
      // 重新加载页面，确保路由完全重置
      window.location.reload()
    }
  }
}

const getters = {
  token: state => state.token,
  userInfo: state => state.userInfo,
  userMenus: state => state.userMenus,
  userPermissions: state => state.userPermissions,
  isLoggedIn: state => !!state.token,
  hasPermission: (state) => (permission) => {
    return state.userPermissions.includes(permission)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
