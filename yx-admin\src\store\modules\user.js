const state = {
  token: localStorage.getItem('token') || '',
  userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}')
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    localStorage.setItem('token', token)
  },
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
  },
  CLEAR_USER_DATA(state) {
    state.token = ''
    state.userInfo = {}
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }
}

const actions = {
  login({ commit }, loginData) {
    return new Promise((resolve, reject) => {
      // 模拟登录API调用
      setTimeout(() => {
        if (loginData.username === 'admin' && loginData.password === '123456') {
          const token = 'mock-token-' + Date.now()
          const userInfo = {
            id: 1,
            username: 'admin',
            name: '管理员',
            role: 'admin'
          }
          commit('SET_TOKEN', token)
          commit('SET_USER_INFO', userInfo)
          resolve({ token, userInfo })
        } else {
          reject(new Error('用户名或密码错误'))
        }
      }, 1000)
    })
  },
  logout({ commit }) {
    commit('CLEAR_USER_DATA')
  }
}

const getters = {
  token: state => state.token,
  userInfo: state => state.userInfo,
  isLoggedIn: state => !!state.token
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
