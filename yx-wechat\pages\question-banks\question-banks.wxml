<!--pages/question-banks/question-banks.wxml-->
<view class="container">
  <!-- 题库列表 -->
  <view class="banks-list" wx:if="{{ questionBanks.length > 0 }}">
    <view 
      class="bank-item" 
      wx:for="{{ questionBanks }}" 
      wx:key="id"
      bind:tap="goToBooks"
      data-bank="{{ item }}"
    >
      <view class="bank-info">
        <view class="bank-name">{{ item.name }}</view>
        <view class="bank-desc">{{ item.description || '暂无描述' }}</view>
        <view class="bank-stats">
          <van-tag size="mini" type="primary">{{ item.bookCount || 0 }}本书籍</van-tag>
          <van-tag size="mini" type="success">{{ item.questionCount || 0 }}道题目</van-tag>
        </view>
      </view>
      <van-icon name="arrow" color="#c8c9cc" />
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <van-icon name="notes-o" size="80rpx" color="#c8c9cc" />
    <view class="empty-text">该学科暂无题库</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <van-loading size="24px" />
    <view class="loading-text">加载中...</view>
  </view>
</view>

<van-toast id="van-toast" />
