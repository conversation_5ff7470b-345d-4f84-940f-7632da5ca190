<!--pages/subjects/subjects.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <van-field
      value="{{ searchKeyword }}"
      placeholder="搜索学科"
      left-icon="search"
      bind:change="onSearchChange"
      bind:confirm="onSearch"
    />
  </view>

  <!-- 学科列表 -->
  <view class="subjects-list" wx:if="{{ subjects.length > 0 }}">
    <view 
      class="subject-item" 
      wx:for="{{ filteredSubjects }}" 
      wx:key="id"
      bind:tap="goToQuestionBanks"
      data-subject="{{ item }}"
    >
      <view class="subject-icon" style="background-color: {{ item.color || '#1989fa' }}">
        <van-icon name="study" color="#fff" size="40rpx" />
      </view>
      <view class="subject-info">
        <view class="subject-name">{{ item.name }}</view>
        <view class="subject-desc">{{ item.description || '暂无描述' }}</view>
        <view class="subject-stats">
          <van-tag size="mini" type="primary">{{ item.questionBankCount || 0 }}个题库</van-tag>
        </view>
      </view>
      <van-icon name="arrow" color="#c8c9cc" />
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <van-icon name="search" size="80rpx" color="#c8c9cc" />
    <view class="empty-text">
      {{ searchKeyword ? '未找到相关学科' : '暂无学科数据' }}
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <van-loading size="24px" />
    <view class="loading-text">加载中...</view>
  </view>
</view>

<van-toast id="van-toast" />
