const Router = require('koa-router');
const { response } = require('../utils/response');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取权限列表
router.get('/', permissionMiddleware('system:permission'), async (ctx) => {
  const permissions = await query(
    'SELECT id, name, code, type, parent_id, path, icon, sort, status, created_time, updated_time FROM permissions ORDER BY sort ASC, created_time DESC'
  );
  
  ctx.body = response.success(permissions);
});

// 获取所有启用的权限（不分页）
router.get('/all', async (ctx) => {
  const permissions = await query(
    'SELECT id, name, code, type, parent_id, path, icon, sort FROM permissions WHERE status = 1 ORDER BY sort ASC, created_time DESC'
  );
  
  ctx.body = response.success(permissions);
});

module.exports = router;
