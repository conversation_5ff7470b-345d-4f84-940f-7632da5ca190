<template>
  <div class="bank-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="题库名称">
            <el-input v-model="searchForm.name" placeholder="请输入题库名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="所属学科">
            <el-select v-model="searchForm.subjectId" placeholder="请选择学科" clearable>
              <el-option 
                v-for="subject in subjectList" 
                :key="subject.id" 
                :label="subject.name" 
                :value="subject.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增题库</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="题库名称"></el-table-column>
        <el-table-column prop="subjectName" label="所属学科" width="120">
          <template slot-scope="scope">
            <el-tag :color="scope.row.subjectColor" style="color: white;">
              {{ scope.row.subjectName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="bookCount" label="书籍数量" width="100">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.bookCount }}本</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="questionCount" label="题目数量" width="100">
          <template slot-scope="scope">
            <el-tag type="success">{{ scope.row.questionCount }}题</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80"></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="primary" @click="handleBooks(scope.row)">书籍</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="题库名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="所属学科" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="请选择学科" style="width: 100%;">
            <el-option 
              v-for="subject in subjectList" 
              :key="subject.id" 
              :label="subject.name" 
              :value="subject.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'QuestionBank',
  data() {
    return {
      loading: false,
      searchForm: {
        name: '',
        subjectId: '',
        status: ''
      },
      tableData: [
        {
          id: 1,
          name: '小学数学题库',
          subjectId: 1,
          subjectName: '数学',
          subjectColor: '#409EFF',
          description: '小学1-6年级数学题库，包含基础运算、应用题等',
          bookCount: 12,
          questionCount: 1580,
          sort: 1,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 2,
          name: '初中数学题库',
          subjectId: 1,
          subjectName: '数学',
          subjectColor: '#409EFF',
          description: '初中数学题库，包含代数、几何、函数等',
          bookCount: 8,
          questionCount: 2340,
          sort: 2,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 3,
          name: '小学语文题库',
          subjectId: 2,
          subjectName: '语文',
          subjectColor: '#67C23A',
          description: '小学语文题库，包含拼音、汉字、阅读理解等',
          bookCount: 10,
          questionCount: 1200,
          sort: 3,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 3
      },
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        name: '',
        subjectId: '',
        description: '',
        sort: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入题库名称', trigger: 'blur' }
        ],
        subjectId: [
          { required: true, message: '请选择所属学科', trigger: 'change' }
        ]
      },
      subjectList: [
        { id: 1, name: '数学' },
        { id: 2, name: '语文' },
        { id: 3, name: '英语' },
        { id: 4, name: '物理' }
      ]
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑题库' : '新增题库'
    }
  },
  methods: {
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        subjectId: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        name: '',
        subjectId: '',
        description: '',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = { ...row }
      this.dialogVisible = true
    },
    handleBooks(row) {
      this.$router.push({
        path: '/question/book',
        query: { bankId: row.id, bankName: row.name }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该题库吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadData()
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
          this.dialogVisible = false
          this.loadData()
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },
    loadData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
      }, 500)
    }
  },
  mounted() {
    this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.bank-management {
  .search-area {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
