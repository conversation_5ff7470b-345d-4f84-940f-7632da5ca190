<template>
  <div class="bank-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="题库名称">
            <el-input v-model="searchForm.name" placeholder="请输入题库名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="所属学科">
            <el-select v-model="searchForm.subjectId" placeholder="请选择学科" clearable>
              <el-option
                v-for="subject in subjectList"
                :key="subject.id"
                :label="subject.name"
                :value="subject.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增题库</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="题库名称"></el-table-column>
        <el-table-column prop="subject_name" label="所属学科" width="120">
          <template slot-scope="scope">
            <el-tag type="primary">
              {{ scope.row.subject_name || '未知' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_time" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ scope.row.created_time ? new Date(scope.row.created_time).toLocaleString() : '' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="primary" @click="handleBooks(scope.row)">书籍</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="题库名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="所属学科" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="请选择学科" style="width: 100%;">
            <el-option
              v-for="subject in subjectList"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api'
import permissionMixin from '@/mixins/permission'

export default {
  name: 'QuestionBank',
  mixins: [permissionMixin],
  data() {
    return {
      loading: false,
      searchForm: {
        name: '',
        subjectId: '',
        status: ''
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        name: '',
        subjectId: '',
        description: '',
        sort: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入题库名称', trigger: 'blur' }
        ],
        subjectId: [
          { required: true, message: '请选择所属学科', trigger: 'change' }
        ]
      },
      subjectList: []
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑题库' : '新增题库'
    }
  },
  methods: {
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        subjectId: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        name: '',
        subjectId: '',
        description: '',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = {
        id: row.id,
        name: row.name,
        subjectId: row.subject_id,
        description: row.description,
        sort: 0,
        status: row.status
      }
      this.dialogVisible = true
    },
    handleBooks(row) {
      this.$router.push({
        path: '/question/book',
        query: { bankId: row.id, bankName: row.name }
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除该题库吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await api.questionBanks.delete(row.id)
        this.$message.success('删除成功')
        this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (valid) {
          if (this.isEdit) {
            await api.questionBanks.update(this.form.id, this.form)
          } else {
            await api.questionBanks.create(this.form)
          }
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
          this.dialogVisible = false
          this.loadData()
        }
      } catch (error) {
        this.$message.error(this.isEdit ? '编辑失败' : '新增失败')
      }
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },
    async loadData() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }
        const response = await api.questionBanks.list(params)
        this.tableData = response.data.list || []
        this.pagination.total = response.data.pagination.total
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    async loadSubjects() {
      try {
        const response = await api.subjects.all()
        this.subjectList = response.data || []
      } catch (error) {
        console.error('加载学科列表失败:', error)
      }
    }
  },
  async mounted() {
    await this.loadSubjects()
    await this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.bank-management {
  .search-area {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
