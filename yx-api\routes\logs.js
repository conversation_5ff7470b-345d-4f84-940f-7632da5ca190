const Router = require('koa-router');
const { response } = require('../utils/response');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取系统日志列表
router.get('/', permissionMiddleware('system:log'), async (ctx) => {
  const { page = 1, pageSize = 10, level, module, startTime, endTime } = ctx.query;

  let whereClause = 'WHERE 1=1';
  const params = [];

  if (level) {
    whereClause += ' AND level = ?';
    params.push(level);
  }

  if (module) {
    whereClause += ' AND module LIKE ?';
    params.push(`%${module}%`);
  }

  if (startTime) {
    whereClause += ' AND created_time >= ?';
    params.push(startTime);
  }

  if (endTime) {
    whereClause += ' AND created_time <= ?';
    params.push(endTime);
  }

  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM system_logs ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;

  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT id, level, module, action, message, ip, user_agent, user_id, created_time
    FROM system_logs
    ${whereClause}
    ORDER BY created_time DESC
    LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
  `;

  const logs = await query(dataSql, params);

  // 查询用户信息
  for (let log of logs) {
    if (log.user_id) {
      const users = await query('SELECT username FROM users WHERE id = ?', [log.user_id]);
      log.username = users.length > 0 ? users[0].username : '未知用户';
    }
  }

  ctx.body = response.page(logs, total, page, pageSize);
});

// 创建系统日志（内部使用）
router.post('/', async (ctx) => {
  const { level, module, action, message, ip, userAgent, userId } = ctx.request.body;

  await query(
    'INSERT INTO system_logs (level, module, action, message, ip, user_agent, user_id, created_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [level, module, action, message, ip, userAgent, userId, new Date()]
  );

  ctx.body = response.success(null, '日志记录成功');
});

module.exports = router;
