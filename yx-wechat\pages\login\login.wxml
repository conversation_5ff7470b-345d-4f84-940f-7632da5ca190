<!--pages/login/login.wxml-->
<view class="container">
  <view class="login-header">
    <view class="logo">
      <van-icon name="study" size="80rpx" color="#1989fa" />
    </view>
    <view class="title">YX刷题</view>
    <view class="subtitle">专业的在线学习平台</view>
  </view>

  <view class="login-form">
    <van-cell-group>
      <van-field
        value="{{ username }}"
        placeholder="请输入用户名"
        border="{{ false }}"
        bind:change="onUsernameChange"
        left-icon="manager"
      />
      <van-field
        value="{{ password }}"
        type="password"
        placeholder="请输入密码"
        border="{{ false }}"
        bind:change="onPasswordChange"
        left-icon="lock"
      />
    </van-cell-group>

    <van-button
      type="primary"
      size="large"
      class="login-btn"
      loading="{{ loading }}"
      bind:click="onLogin"
    >
      登录
    </van-button>

    <view class="register-link">
      <text>还没有账号？</text>
      <text class="link" bind:tap="goToRegister">立即注册</text>
    </view>
  </view>
</view>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
