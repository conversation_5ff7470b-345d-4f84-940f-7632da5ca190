# 图标资源说明

此目录存放小程序所需的图标资源。

## 所需图标列表

### TabBar 图标 (建议尺寸: 81x81px)
- `home.png` - 首页图标（未选中）
- `home-active.png` - 首页图标（选中）
- `subject.png` - 学科图标（未选中）
- `subject-active.png` - 学科图标（选中）
- `chart.png` - 统计图标（未选中）
- `chart-active.png` - 统计图标（选中）
- `user.png` - 用户图标（未选中）
- `user-active.png` - 用户图标（选中）

## 图标规范

1. **格式**: PNG格式，支持透明背景
2. **尺寸**: TabBar图标建议81x81px
3. **颜色**: 
   - 未选中状态：#7A7E83
   - 选中状态：#1989fa
4. **设计风格**: 简洁、清晰，符合微信小程序设计规范

## 使用说明

请将对应的图标文件放置在此目录下，确保文件名与app.json中tabBar配置的路径一致。

如果暂时没有图标资源，可以：
1. 使用在线图标生成工具创建简单图标
2. 从免费图标库下载合适的图标
3. 使用Vant Weapp的内置图标作为临时替代
