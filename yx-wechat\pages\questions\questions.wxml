<!--pages/questions/questions.wxml-->
<view class="container">
  <!-- 进度条 -->
  <view class="progress-bar" wx:if="{{ questions.length > 0 }}">
    <view class="progress-info">
      <text>{{ currentIndex + 1 }} / {{ questions.length }}</text>
      <text>{{ Math.round(((currentIndex + 1) / questions.length) * 100) }}%</text>
    </view>
    <van-progress 
      percentage="{{ ((currentIndex + 1) / questions.length) * 100 }}" 
      stroke-width="8rpx"
      color="#1989fa"
    />
  </view>

  <!-- 题目内容 -->
  <view class="question-content" wx:if="{{ currentQuestion }}">
    <view class="question-header">
      <van-tag type="primary" size="mini">{{ getQuestionTypeText(currentQuestion.type) }}</van-tag>
      <van-tag type="success" size="mini" wx:if="{{ currentQuestion.difficulty }}">
        {{ getDifficultyText(currentQuestion.difficulty) }}
      </van-tag>
    </view>

    <view class="question-title">
      {{ currentQuestion.content }}
    </view>

    <!-- 单选题 -->
    <view class="options" wx:if="{{ currentQuestion.type === 'single' }}">
      <van-radio-group value="{{ selectedAnswer }}" bind:change="onSingleChoiceChange">
        <van-radio 
          wx:for="{{ currentQuestion.options }}" 
          wx:key="index"
          name="{{ item.key }}"
          custom-class="option-item"
        >
          {{ item.key }}. {{ item.value }}
        </van-radio>
      </van-radio-group>
    </view>

    <!-- 多选题 -->
    <view class="options" wx:elif="{{ currentQuestion.type === 'multiple' }}">
      <van-checkbox-group value="{{ selectedAnswers }}" bind:change="onMultipleChoiceChange">
        <van-checkbox 
          wx:for="{{ currentQuestion.options }}" 
          wx:key="index"
          name="{{ item.key }}"
          custom-class="option-item"
        >
          {{ item.key }}. {{ item.value }}
        </van-checkbox>
      </van-checkbox-group>
    </view>

    <!-- 判断题 -->
    <view class="options" wx:elif="{{ currentQuestion.type === 'judge' }}">
      <van-radio-group value="{{ selectedAnswer }}" bind:change="onSingleChoiceChange">
        <van-radio name="true" custom-class="option-item">正确</van-radio>
        <van-radio name="false" custom-class="option-item">错误</van-radio>
      </van-radio-group>
    </view>

    <!-- 填空题 -->
    <view class="fill-blank" wx:elif="{{ currentQuestion.type === 'fill' }}">
      <van-field
        value="{{ fillAnswer }}"
        placeholder="请输入答案"
        bind:change="onFillAnswerChange"
        type="textarea"
        autosize
      />
    </view>

    <!-- 简答题 -->
    <view class="essay" wx:elif="{{ currentQuestion.type === 'essay' }}">
      <van-field
        value="{{ essayAnswer }}"
        placeholder="请输入答案"
        bind:change="onEssayAnswerChange"
        type="textarea"
        autosize
        maxlength="1000"
        show-word-limit
      />
    </view>

    <!-- 答案解析 -->
    <view class="answer-analysis" wx:if="{{ showAnalysis }}">
      <van-divider>答案解析</van-divider>
      <view class="correct-answer">
        <text class="label">正确答案：</text>
        <text class="answer">{{ getCorrectAnswerText() }}</text>
      </view>
      <view class="analysis-content" wx:if="{{ currentQuestion.analysis }}">
        <text class="label">解析：</text>
        <text class="content">{{ currentQuestion.analysis }}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{ currentQuestion }}">
    <van-button 
      wx:if="{{ !showAnalysis }}"
      type="primary" 
      size="large"
      bind:click="submitAnswer"
      disabled="{{ !hasAnswer() }}"
    >
      提交答案
    </van-button>
    
    <view class="button-group" wx:else>
      <van-button 
        wx:if="{{ currentIndex > 0 }}"
        type="default" 
        bind:click="prevQuestion"
      >
        上一题
      </van-button>
      <van-button 
        wx:if="{{ currentIndex < questions.length - 1 }}"
        type="primary" 
        bind:click="nextQuestion"
      >
        下一题
      </van-button>
      <van-button 
        wx:else
        type="primary" 
        bind:click="finishPractice"
      >
        完成练习
      </van-button>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <van-icon name="question-o" size="80rpx" color="#c8c9cc" />
    <view class="empty-text">该章节暂无题目</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <van-loading size="24px" />
    <view class="loading-text">加载中...</view>
  </view>
</view>

<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
