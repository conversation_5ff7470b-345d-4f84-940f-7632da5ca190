/* pages/questions/questions.wxss */
.container {
  padding: 24rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 进度条 */
.progress-bar {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #323233;
}

/* 题目内容 */
.question-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  flex: 1;
}

.question-header {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.question-title {
  font-size: 32rpx;
  line-height: 1.6;
  color: #323233;
  margin-bottom: 32rpx;
}

/* 选项样式 */
.options {
  margin-bottom: 32rpx;
}

.option-item {
  margin-bottom: 24rpx !important;
  padding: 24rpx !important;
  background: #f7f8fa !important;
  border-radius: 12rpx !important;
  border: 2rpx solid transparent !important;
  transition: all 0.2s !important;
}

.option-item.van-radio--checked,
.option-item.van-checkbox--checked {
  background: #e8f3ff !important;
  border-color: #1989fa !important;
}

/* 填空题和简答题 */
.fill-blank,
.essay {
  margin-bottom: 32rpx;
}

/* 答案解析 */
.answer-analysis {
  margin-top: 32rpx;
  padding-top: 32rpx;
}

.correct-answer,
.analysis-content {
  margin-bottom: 24rpx;
  line-height: 1.6;
}

.label {
  font-weight: 600;
  color: #323233;
}

.answer {
  color: #07c160;
  font-weight: 500;
}

.content {
  color: #646566;
}

/* 操作按钮 */
.action-buttons {
  padding: 24rpx 0;
}

.button-group {
  display: flex;
  gap: 24rpx;
}

.button-group .van-button {
  flex: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 24rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
  flex: 1;
  justify-content: center;
}

.loading-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 16rpx;
}
