-- YX刷题小程序数据库初始化脚本

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` text COMMENT '角色描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限编码',
  `type` varchar(20) DEFAULT 'menu' COMMENT '权限类型：menu-菜单，button-按钮，api-接口',
  `parent_id` int(11) DEFAULT 0 COMMENT '父权限ID',
  `path` varchar(255) DEFAULT NULL COMMENT '路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_role` (`user_id`, `role_id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `permission_id` int(11) NOT NULL COMMENT '权限ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_permission` (`role_id`, `permission_id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 菜单表
CREATE TABLE IF NOT EXISTS `menus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `path` varchar(255) DEFAULT NULL COMMENT '菜单路径',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `parent_id` int(11) DEFAULT 0 COMMENT '父菜单ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `type` varchar(20) DEFAULT 'menu' COMMENT '类型：menu-菜单，button-按钮',
  `permission` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-显示，0-隐藏',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';

-- 系统日志表
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
  `operation` varchar(100) NOT NULL COMMENT '操作类型',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `url` varchar(255) NOT NULL COMMENT '请求URL',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `status` int(11) DEFAULT NULL COMMENT '响应状态码',
  `duration` int(11) DEFAULT NULL COMMENT '执行时间(ms)',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '登录状态：1-成功，0-失败',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- 学科表
CREATE TABLE IF NOT EXISTS `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '学科名称',
  `code` varchar(50) NOT NULL COMMENT '学科编码',
  `icon` varchar(255) DEFAULT NULL COMMENT '学科图标',
  `color` varchar(20) DEFAULT NULL COMMENT '主题色',
  `description` text COMMENT '学科描述',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学科表';

-- 题库表
CREATE TABLE IF NOT EXISTS `question_banks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '题库名称',
  `subject_id` int(11) NOT NULL COMMENT '学科ID',
  `description` text COMMENT '题库描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题库表';

-- 书籍表
CREATE TABLE IF NOT EXISTS `books` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '书籍名称',
  `question_bank_id` int(11) NOT NULL COMMENT '题库ID',
  `grade` varchar(20) DEFAULT NULL COMMENT '年级',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `description` text COMMENT '书籍描述',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `question_bank_id` (`question_bank_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='书籍表';

-- 章节表
CREATE TABLE IF NOT EXISTS `chapters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '章节名称',
  `book_id` int(11) NOT NULL COMMENT '书籍ID',
  `parent_id` int(11) DEFAULT 0 COMMENT '父章节ID，0表示章',
  `type` varchar(20) DEFAULT 'chapter' COMMENT '类型：chapter-章，section-节',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `description` text COMMENT '章节描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `book_id` (`book_id`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='章节表';

-- 题目分类表
CREATE TABLE IF NOT EXISTS `question_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(50) NOT NULL COMMENT '分类编码',
  `type` varchar(20) NOT NULL COMMENT '题目类型：single-单选，multiple-多选，judge-判断，fill-填空，essay-简答',
  `difficulty` tinyint(1) DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
  `description` text COMMENT '分类描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目分类表';

-- 题目表
CREATE TABLE IF NOT EXISTS `questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` text NOT NULL COMMENT '题目标题',
  `content` text COMMENT '题目内容',
  `chapter_id` int(11) NOT NULL COMMENT '章节ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `type` varchar(20) NOT NULL COMMENT '题目类型：single-单选，multiple-多选，judge-判断，fill-填空，essay-简答',
  `difficulty` tinyint(1) DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
  `score` decimal(5,2) DEFAULT 1.00 COMMENT '分值',
  `answer` text COMMENT '正确答案',
  `analysis` text COMMENT '答案解析',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `chapter_id` (`chapter_id`),
  KEY `category_id` (`category_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目表';

-- 题目选项表（用于单选题和多选题）
CREATE TABLE IF NOT EXISTS `question_options` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL COMMENT '题目ID',
  `option_key` varchar(10) NOT NULL COMMENT '选项标识（A、B、C、D等）',
  `option_text` text NOT NULL COMMENT '选项内容',
  `is_correct` tinyint(1) DEFAULT 0 COMMENT '是否正确答案：1-是，0-否',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目选项表';

-- 插入初始数据

-- 插入默认管理员用户 (密码: 123456)
INSERT INTO `users` (`username`, `email`, `password`, `status`, `created_time`) VALUES
('admin', '<EMAIL>', '$2a$10$wM5dgQwmTlo00bHrrdRyQeuYgdxuNemow9Egird2UV2/VoDbe609.', 1, NOW());

-- 插入默认角色
INSERT INTO `roles` (`name`, `code`, `description`, `status`, `created_time`) VALUES
('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1, NOW()),
('管理员', 'admin', '系统管理员', 1, NOW()),
('教师', 'teacher', '教师角色，可以管理题库和题目', 1, NOW()),
('学生', 'student', '学生角色，只能查看和答题', 1, NOW());

-- 插入默认权限
INSERT INTO `permissions` (`name`, `code`, `type`, `parent_id`, `path`, `icon`, `sort`, `status`, `created_time`) VALUES
-- 系统管理
('系统管理', 'system', 'menu', 0, '/system', 'el-icon-setting', 1, 1, NOW()),
('用户管理', 'system:user', 'menu', 1, '/system/user', 'el-icon-user', 1, 1, NOW()),
('角色管理', 'system:role', 'menu', 1, '/system/role', 'el-icon-s-custom', 2, 1, NOW()),
('菜单管理', 'system:menu', 'menu', 1, '/system/menu', 'el-icon-menu', 3, 1, NOW()),
('权限管理', 'system:permission', 'menu', 1, '/system/permission', 'el-icon-key', 4, 1, NOW()),
('系统日志', 'system:log', 'menu', 1, '/system/log', 'el-icon-document', 5, 1, NOW()),
('学科管理', 'system:subject', 'menu', 1, '/system/subject', 'el-icon-collection', 6, 1, NOW()),

-- 题库管理
('题库管理', 'question', 'menu', 0, '/question', 'el-icon-notebook-1', 2, 1, NOW()),
('题库列表', 'question:bank', 'menu', 8, '/question/bank', 'el-icon-folder', 1, 1, NOW()),
('书籍管理', 'question:book', 'menu', 8, '/question/book', 'el-icon-reading', 2, 1, NOW()),
('章节管理', 'question:chapter', 'menu', 8, '/question/chapter', 'el-icon-document-copy', 3, 1, NOW()),
('题目分类', 'question:category', 'menu', 8, '/question/category', 'el-icon-files', 4, 1, NOW()),
('题目列表', 'question:list', 'menu', 8, '/question/list', 'el-icon-edit-outline', 5, 1, NOW());

-- 分配超级管理员角色给admin用户
INSERT INTO `user_roles` (`user_id`, `role_id`, `created_time`) VALUES
(1, 1, NOW());

-- 分配所有权限给超级管理员角色
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `created_time`)
SELECT 1, id, NOW() FROM `permissions` WHERE `status` = 1;
