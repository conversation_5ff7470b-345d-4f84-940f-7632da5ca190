<!--pages/index/index.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card" wx:if="{{ userInfo }}">
    <view class="user-info">
      <view class="avatar">
        <van-icon name="user-circle-o" size="60rpx" color="#1989fa" />
      </view>
      <view class="info">
        <view class="name">{{ userInfo.username }}</view>
        <view class="desc">继续加油学习吧！</view>
      </view>
    </view>
    <view class="stats">
      <view class="stat-item">
        <view class="number">{{ statistics.total }}</view>
        <view class="label">已练习</view>
      </view>
      <view class="stat-item">
        <view class="number">{{ statistics.correct }}</view>
        <view class="label">答对题</view>
      </view>
      <view class="stat-item">
        <view class="number">{{ statistics.correctRate }}%</view>
        <view class="label">正确率</view>
      </view>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view class="login-prompt" wx:else>
    <van-icon name="user-circle-o" size="80rpx" color="#c8c9cc" />
    <view class="prompt-text">请先登录</view>
    <van-button type="primary" size="small" bind:click="goToLogin">
      立即登录
    </van-button>
  </view>

  <!-- 快速入口 -->
  <view class="quick-actions">
    <view class="section-title">快速入口</view>
    <van-grid column-num="2" border="{{ false }}" gutter="24rpx">
      <van-grid-item
        icon="study"
        text="开始学习"
        bind:click="goToSubjects"
      />
      <van-grid-item
        icon="chart-trending-o"
        text="学习统计"
        bind:click="goToStatistics"
      />
    </van-grid>
  </view>

  <!-- 最近学习 -->
  <view class="recent-study" wx:if="{{ recentSubjects.length > 0 }}">
    <view class="section-title">最近学习</view>
    <view class="subject-list">
      <view 
        class="subject-item" 
        wx:for="{{ recentSubjects }}" 
        wx:key="id"
        bind:tap="goToQuestionBanks"
        data-subject="{{ item }}"
      >
        <view class="subject-info">
          <view class="subject-name">{{ item.name }}</view>
          <view class="subject-desc">{{ item.description }}</view>
        </view>
        <van-icon name="arrow" color="#c8c9cc" />
      </view>
    </view>
  </view>

  <!-- 推荐学科 -->
  <view class="recommended" wx:if="{{ recommendedSubjects.length > 0 }}">
    <view class="section-title">推荐学科</view>
    <view class="subject-grid">
      <view 
        class="subject-card" 
        wx:for="{{ recommendedSubjects }}" 
        wx:key="id"
        bind:tap="goToQuestionBanks"
        data-subject="{{ item }}"
      >
        <view class="card-icon" style="background-color: {{ item.color || '#1989fa' }}">
          <van-icon name="study" color="#fff" size="32rpx" />
        </view>
        <view class="card-name">{{ item.name }}</view>
      </view>
    </view>
  </view>
</view>

<van-toast id="van-toast" />
