/* pages/index/index.wxss */
.container {
  padding: 24rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  color: #fff;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.avatar {
  margin-right: 24rpx;
}

.name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.desc {
  font-size: 28rpx;
  opacity: 0.8;
}

.stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 未登录状态 */
.login-prompt {
  text-align: center;
  padding: 80rpx 40rpx;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
}

.prompt-text {
  font-size: 32rpx;
  color: #969799;
  margin: 24rpx 0 32rpx;
}

/* 快速入口 */
.quick-actions {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
}

/* 最近学习 */
.recent-study {
  margin-bottom: 32rpx;
}

.subject-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.subject-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.subject-item:last-child {
  border-bottom: none;
}

.subject-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #323233;
  margin-bottom: 8rpx;
}

.subject-desc {
  font-size: 28rpx;
  color: #969799;
}

/* 推荐学科 */
.recommended {
  margin-bottom: 32rpx;
}

.subject-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.subject-card {
  flex: 1;
  min-width: 200rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16rpx;
}

.card-name {
  font-size: 28rpx;
  color: #323233;
}
