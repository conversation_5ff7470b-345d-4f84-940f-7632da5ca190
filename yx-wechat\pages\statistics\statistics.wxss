/* pages/statistics/statistics.wxss */
.container {
  padding: 24rpx;
}

/* 总体统计 */
.stats-overview {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.stats-card {
  flex: 1;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #969799;
}

/* 进度部分 */
.progress-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 24rpx;
}

.progress-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

.progress-info {
  margin-bottom: 24rpx;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #323233;
}

.rate {
  font-weight: 600;
  color: #07c160;
}

.progress-detail {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  text-align: center;
}

.label {
  font-size: 24rpx;
  color: #969799;
}

.value {
  font-size: 28rpx;
  font-weight: 500;
  color: #323233;
}

.value.correct {
  color: #07c160;
}

.value.wrong {
  color: #ee0a24;
}

/* 最近记录 */
.recent-records {
  margin-bottom: 32rpx;
}

.records-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-question {
  font-size: 28rpx;
  color: #323233;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #969799;
}

/* 学习建议 */
.suggestions {
  margin-bottom: 32rpx;
}

.suggestion-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

.suggestion-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 28rpx;
  color: #646566;
  line-height: 1.5;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-item .van-icon {
  margin-right: 16rpx;
  flex-shrink: 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin: 24rpx 0 32rpx;
}
