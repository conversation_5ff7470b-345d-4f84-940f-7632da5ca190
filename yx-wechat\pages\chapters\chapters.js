// pages/chapters/chapters.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')
const storage = require('../../utils/storage.js')
import Toast from '@vant/weapp/toast/toast'

Page({
  data: {
    subjectId: '',
    subjectName: '',
    bankId: '',
    bankName: '',
    bookId: '',
    bookName: '',
    chapters: [],
    loading: false
  },

  onLoad(options) {
    const { subjectId, subjectName, bankId, bankName, bookId, bookName } = options
    
    if (!subjectId || !bankId || !bookId) {
      Toast.fail('参数错误')
      wx.navigateBack()
      return
    }

    this.setData({
      subjectId,
      subjectName: decodeURIComponent(subjectName || ''),
      bankId,
      bankName: decodeURIComponent(bankName || ''),
      bookId,
      bookName: decodeURIComponent(bookName || '')
    })

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.bookName || '章节'
    })

    this.checkLoginAndLoadData()
  },

  onShow() {
    // 每次显示页面时重新加载进度
    this.loadChaptersProgress()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    if (!auth.requireLogin()) return
    this.loadChapters()
  },

  // 加载章节列表
  loadChapters() {
    this.setData({ loading: true })
    
    api.chapters.list({ bookId: this.data.bookId })
      .then(res => {
        const chapters = res.data?.list || []
        this.setData({ chapters })
        this.loadChaptersProgress()
      })
      .catch(err => {
        console.error('加载章节失败:', err)
        Toast.fail('加载章节失败')
      })
      .finally(() => {
        this.setData({ loading: false })
      })
  },

  // 加载章节进度
  loadChaptersProgress() {
    const { chapters, subjectId, bankId, bookId } = this.data
    
    const updatedChapters = chapters.map(chapter => {
      const progress = storage.progress.getProgress(subjectId, bankId, bookId, chapter.id)
      return {
        ...chapter,
        progress: progress.total > 0 ? Math.round((progress.completed / progress.total) * 100) : 0,
        completedCount: progress.completed
      }
    })

    this.setData({ chapters: updatedChapters })
  },

  // 跳转到题目页面
  goToQuestions(e) {
    const chapter = e.currentTarget.dataset.chapter
    const params = {
      subjectId: this.data.subjectId,
      subjectName: this.data.subjectName,
      bankId: this.data.bankId,
      bankName: this.data.bankName,
      bookId: this.data.bookId,
      bookName: this.data.bookName,
      chapterId: chapter.id,
      chapterName: chapter.name
    }
    
    const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    
    wx.navigateTo({
      url: `/pages/questions/questions?${query}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadChapters()
    wx.stopPullDownRefresh()
  }
})
