// pages/subjects/subjects.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')
import Toast from '@vant/weapp/toast/toast'

Page({
  data: {
    subjects: [],
    filteredSubjects: [],
    searchKeyword: '',
    loading: false
  },

  onLoad() {
    this.checkLoginAndLoadData()
  },

  onShow() {
    this.checkLoginAndLoadData()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    if (!auth.requireLogin()) return
    this.loadSubjects()
  },

  // 加载学科列表
  loadSubjects() {
    this.setData({ loading: true })
    
    api.subjects.all()
      .then(res => {
        const subjects = res.data || []
        this.setData({
          subjects,
          filteredSubjects: subjects
        })
      })
      .catch(err => {
        console.error('加载学科失败:', err)
        Toast.fail('加载学科失败')
      })
      .finally(() => {
        this.setData({ loading: false })
      })
  },

  // 搜索输入
  onSearchChange(event) {
    const keyword = event.detail
    this.setData({ searchKeyword: keyword })
    this.filterSubjects(keyword)
  },

  // 搜索确认
  onSearch() {
    this.filterSubjects(this.data.searchKeyword)
  },

  // 过滤学科
  filterSubjects(keyword) {
    const { subjects } = this.data
    
    if (!keyword.trim()) {
      this.setData({ filteredSubjects: subjects })
      return
    }

    const filtered = subjects.filter(subject => 
      subject.name.toLowerCase().includes(keyword.toLowerCase()) ||
      (subject.description && subject.description.toLowerCase().includes(keyword.toLowerCase()))
    )

    this.setData({ filteredSubjects: filtered })
  },

  // 跳转到题库页面
  goToQuestionBanks(e) {
    const subject = e.currentTarget.dataset.subject
    wx.navigateTo({
      url: `/pages/question-banks/question-banks?subjectId=${subject.id}&subjectName=${subject.name}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadSubjects()
    wx.stopPullDownRefresh()
  }
})
