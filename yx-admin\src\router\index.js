import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'
import Login from '@/views/Login.vue'
import Layout from '@/layout/index.vue'

Vue.use(VueRouter)

// 常量路由（不需要权限的路由）
export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '首页', icon: 'el-icon-s-home' }
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/404.vue'),
    hidden: true
  }
]

// 异步路由（需要权限的路由）
export const asyncRoutes = [
  {
    path: '/system',
    component: Layout,
    name: 'System',
    meta: { title: '系统管理', icon: 'el-icon-setting', permission: 'system' },
    children: [
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/system/User.vue'),
        meta: { title: '用户管理', icon: 'el-icon-user', permission: 'system:user' }
      },
      {
        path: 'role',
        name: 'Role',
        component: () => import('@/views/system/Role.vue'),
        meta: { title: '角色管理', icon: 'el-icon-s-custom', permission: 'system:role' }
      },
      {
        path: 'menu',
        name: 'Menu',
        component: () => import('@/views/system/Menu.vue'),
        meta: { title: '菜单管理', icon: 'el-icon-menu', permission: 'system:menu' }
      },
      {
        path: 'permission',
        name: 'Permission',
        component: () => import('@/views/system/Permission.vue'),
        meta: { title: '权限管理', icon: 'el-icon-key', permission: 'system:permission' }
      },
      {
        path: 'log',
        name: 'Log',
        component: () => import('@/views/system/Log.vue'),
        meta: { title: '系统日志', icon: 'el-icon-document', permission: 'system:log' }
      },
      {
        path: 'subject',
        name: 'Subject',
        component: () => import('@/views/system/Subject.vue'),
        meta: { title: '学科管理', icon: 'el-icon-collection', permission: 'system:subject' }
      }
    ]
  },
  {
    path: '/question',
    component: Layout,
    name: 'Question',
    meta: { title: '题库管理', icon: 'el-icon-notebook-2', permission: 'question' },
    children: [
      {
        path: 'bank',
        name: 'QuestionBank',
        component: () => import('@/views/question/Bank.vue'),
        meta: { title: '题库管理', icon: 'el-icon-folder', permission: 'question:bank' }
      },
      {
        path: 'book',
        name: 'Book',
        component: () => import('@/views/question/Book.vue'),
        meta: { title: '书籍管理', icon: 'el-icon-reading', permission: 'question:book' }
      },
      {
        path: 'chapter',
        name: 'Chapter',
        component: () => import('@/views/question/Chapter.vue'),
        meta: { title: '章节管理', icon: 'el-icon-menu', permission: 'question:chapter' }
      },
      {
        path: 'category',
        name: 'QuestionCategory',
        component: () => import('@/views/question/Category.vue'),
        meta: { title: '题目分类', icon: 'el-icon-files', permission: 'question:category' }
      },
      {
        path: 'list',
        name: 'QuestionList',
        component: () => import('@/views/question/List.vue'),
        meta: { title: '题目列表', icon: 'el-icon-document-copy', permission: 'question:list' }
      }
    ]
  },
  // 404页面必须放在最后
  { path: '*', redirect: '/404', hidden: true }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: constantRoutes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const token = store.getters['user/token']
  const hasGetUserInfo = Object.keys(store.getters['user/userInfo']).length > 0

  if (to.path === '/login') {
    if (token) {
      next('/')
    } else {
      next()
    }
  } else {
    if (token) {
      if (hasGetUserInfo) {
        // 检查是否已经生成了动态路由
        if (store.getters['permission/addRoutes'].length === 0) {
          try {
            // 获取用户菜单
            const menus = await store.dispatch('user/getUserMenus')

            // 生成可访问的路由表
            const accessRoutes = await store.dispatch('permission/generateRoutes', {
              menus,
              permissions: store.getters['user/userPermissions']
            })

            // 动态添加可访问路由
            router.addRoutes(accessRoutes)

            // hack方法 确保addRoutes已完成
            next({ ...to, replace: true })
          } catch (error) {
            // 移除token并跳转到登录页面重新登录
            await store.dispatch('user/logout')
            next(`/login?redirect=${to.path}`)
          }
        } else {
          next()
        }
      } else {
        try {
          // 获取用户信息
          await store.dispatch('user/getUserInfo')

          // 获取用户菜单
          const menus = await store.dispatch('user/getUserMenus')

          // 生成可访问的路由表
          const accessRoutes = await store.dispatch('permission/generateRoutes', {
            menus,
            permissions: store.getters['user/userPermissions']
          })

          // 动态添加可访问路由
          router.addRoutes(accessRoutes)

          // hack方法 确保addRoutes已完成
          next({ ...to, replace: true })
        } catch (error) {
          // 移除token并跳转到登录页面重新登录
          await store.dispatch('user/logout')
          next(`/login?redirect=${to.path}`)
        }
      }
    } else {
      next('/login')
    }
  }
})

export default router
