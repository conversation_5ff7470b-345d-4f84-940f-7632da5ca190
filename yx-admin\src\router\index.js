import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '@/views/Login.vue'
import Layout from '@/layout/index.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '首页', icon: 'el-icon-s-home' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    name: 'System',
    meta: { title: '系统管理', icon: 'el-icon-setting' },
    children: [
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/system/User.vue'),
        meta: { title: '用户管理', icon: 'el-icon-user' }
      },
      {
        path: 'role',
        name: 'Role',
        component: () => import('@/views/system/Role.vue'),
        meta: { title: '角色管理', icon: 'el-icon-s-custom' }
      },
      {
        path: 'menu',
        name: 'Menu',
        component: () => import('@/views/system/Menu.vue'),
        meta: { title: '菜单管理', icon: 'el-icon-menu' }
      },
      {
        path: 'permission',
        name: 'Permission',
        component: () => import('@/views/system/Permission.vue'),
        meta: { title: '权限管理', icon: 'el-icon-key' }
      },
      {
        path: 'log',
        name: 'Log',
        component: () => import('@/views/system/Log.vue'),
        meta: { title: '系统日志', icon: 'el-icon-document' }
      },
      {
        path: 'subject',
        name: 'Subject',
        component: () => import('@/views/system/Subject.vue'),
        meta: { title: '学科管理', icon: 'el-icon-collection' }
      }
    ]
  },
  {
    path: '/question',
    component: Layout,
    name: 'Question',
    meta: { title: '题库管理', icon: 'el-icon-notebook-2' },
    children: [
      {
        path: 'bank',
        name: 'QuestionBank',
        component: () => import('@/views/question/Bank.vue'),
        meta: { title: '题库管理', icon: 'el-icon-folder' }
      },
      {
        path: 'book',
        name: 'Book',
        component: () => import('@/views/question/Book.vue'),
        meta: { title: '书籍管理', icon: 'el-icon-reading' }
      },
      {
        path: 'chapter',
        name: 'Chapter',
        component: () => import('@/views/question/Chapter.vue'),
        meta: { title: '章节管理', icon: 'el-icon-menu' }
      },
      {
        path: 'category',
        name: 'QuestionCategory',
        component: () => import('@/views/question/Category.vue'),
        meta: { title: '题目分类', icon: 'el-icon-files' }
      },
      {
        path: 'list',
        name: 'QuestionList',
        component: () => import('@/views/question/List.vue'),
        meta: { title: '题目列表', icon: 'el-icon-document-copy' }
      }
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')

  if (to.path === '/login') {
    if (token) {
      next('/')
    } else {
      next()
    }
  } else {
    if (token) {
      next()
    } else {
      next('/login')
    }
  }
})

export default router
