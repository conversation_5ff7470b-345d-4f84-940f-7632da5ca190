const Router = require('koa-router');
const { response } = require('../utils/response');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取题目分类列表（简单实现）
router.get('/', permissionMiddleware('question:category'), async (ctx) => {
  ctx.body = response.success([], '题目分类列表');
});

module.exports = router;
