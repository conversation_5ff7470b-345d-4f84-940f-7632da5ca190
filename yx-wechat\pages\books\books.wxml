<!--pages/books/books.wxml-->
<view class="container">
  <!-- 书籍列表 -->
  <view class="books-list" wx:if="{{ books.length > 0 }}">
    <view 
      class="book-item" 
      wx:for="{{ books }}" 
      wx:key="id"
      bind:tap="goToChapters"
      data-book="{{ item }}"
    >
      <view class="book-cover">
        <van-icon name="notes-o" size="40rpx" color="#1989fa" />
      </view>
      <view class="book-info">
        <view class="book-name">{{ item.name }}</view>
        <view class="book-meta">
          <van-tag size="mini" type="primary">{{ item.grade || '通用' }}</van-tag>
          <van-tag size="mini" type="success">{{ item.version || '标准版' }}</van-tag>
        </view>
        <view class="book-stats">
          <text class="stat-text">{{ item.chapterCount || 0 }}个章节</text>
          <text class="stat-text">{{ item.questionCount || 0 }}道题目</text>
        </view>
      </view>
      <van-icon name="arrow" color="#c8c9cc" />
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <van-icon name="notes-o" size="80rpx" color="#c8c9cc" />
    <view class="empty-text">该题库暂无书籍</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <van-loading size="24px" />
    <view class="loading-text">加载中...</view>
  </view>
</view>

<van-toast id="van-toast" />
