<template>
  <div class="log-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="操作用户">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
          </el-form-item>
          <el-form-item label="操作类型">
            <el-select v-model="searchForm.type" placeholder="请选择操作类型" clearable>
              <el-option label="登录" value="login"></el-option>
              <el-option label="登出" value="logout"></el-option>
              <el-option label="新增" value="create"></el-option>
              <el-option label="编辑" value="update"></el-option>
              <el-option label="删除" value="delete"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作模块">
            <el-select v-model="searchForm.module" placeholder="请选择操作模块" clearable>
              <el-option label="用户管理" value="user"></el-option>
              <el-option label="角色管理" value="role"></el-option>
              <el-option label="菜单管理" value="menu"></el-option>
              <el-option label="权限管理" value="permission"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="danger" @click="handleClear">清空日志</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="username" label="操作用户" width="120"></el-table-column>
        <el-table-column prop="type" label="操作类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getTypeColor(scope.row.type)">
              {{ getTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="操作模块" width="120">
          <template slot-scope="scope">
            <el-tag type="info">{{ getModuleName(scope.row.module) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="ip" label="IP地址" width="120"></el-table-column>
        <el-table-column prop="userAgent" label="用户代理" show-overflow-tooltip width="200"></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="操作时间" width="160"></el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleDetail(scope.row)">详情</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="detailDialogVisible" width="800px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="操作用户">{{ currentLog.username }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ getTypeName(currentLog.type) }}</el-descriptions-item>
        <el-descriptions-item label="操作模块">{{ getModuleName(currentLog.module) }}</el-descriptions-item>
        <el-descriptions-item label="操作状态">
          <el-tag :type="currentLog.status === 'success' ? 'success' : 'danger'">
            {{ currentLog.status === 'success' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentLog.ip }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ currentLog.createTime }}</el-descriptions-item>
        <el-descriptions-item label="操作描述" :span="2">{{ currentLog.description }}</el-descriptions-item>
        <el-descriptions-item label="用户代理" :span="2">{{ currentLog.userAgent }}</el-descriptions-item>
        <el-descriptions-item label="请求参数" :span="2">
          <pre>{{ currentLog.params || '无' }}</pre>
        </el-descriptions-item>
        <el-descriptions-item label="响应结果" :span="2">
          <pre>{{ currentLog.result || '无' }}</pre>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Log',
  data() {
    return {
      loading: false,
      searchForm: {
        username: '',
        type: '',
        module: '',
        dateRange: []
      },
      tableData: [
        {
          id: 1,
          username: 'admin',
          type: 'login',
          module: 'auth',
          description: '用户登录系统',
          ip: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'success',
          createTime: '2024-01-01 10:00:00',
          params: '{"username":"admin","password":"******"}',
          result: '{"code":200,"message":"登录成功"}'
        },
        {
          id: 2,
          username: 'admin',
          type: 'create',
          module: 'user',
          description: '新增用户：user1',
          ip: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'success',
          createTime: '2024-01-01 10:30:00',
          params: '{"username":"user1","name":"用户1","email":"<EMAIL>"}',
          result: '{"code":200,"message":"创建成功","data":{"id":2}}'
        },
        {
          id: 3,
          username: 'admin',
          type: 'update',
          module: 'role',
          description: '编辑角色：普通用户',
          ip: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'success',
          createTime: '2024-01-01 11:00:00',
          params: '{"id":2,"name":"普通用户","description":"更新描述"}',
          result: '{"code":200,"message":"更新成功"}'
        },
        {
          id: 4,
          username: 'admin',
          type: 'delete',
          module: 'menu',
          description: '删除菜单：测试菜单',
          ip: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'failed',
          createTime: '2024-01-01 11:30:00',
          params: '{"id":999}',
          result: '{"code":404,"message":"菜单不存在"}'
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 4
      },
      detailDialogVisible: false,
      currentLog: {}
    }
  },
  methods: {
    getTypeName(type) {
      const typeMap = {
        login: '登录',
        logout: '登出',
        create: '新增',
        update: '编辑',
        delete: '删除'
      }
      return typeMap[type] || type
    },
    getTypeColor(type) {
      const colorMap = {
        login: 'success',
        logout: 'info',
        create: 'primary',
        update: 'warning',
        delete: 'danger'
      }
      return colorMap[type] || ''
    },
    getModuleName(module) {
      const moduleMap = {
        auth: '认证',
        user: '用户管理',
        role: '角色管理',
        menu: '菜单管理',
        permission: '权限管理'
      }
      return moduleMap[module] || module
    },
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },
    handleReset() {
      this.searchForm = {
        username: '',
        type: '',
        module: '',
        dateRange: []
      }
      this.loadData()
    },
    handleClear() {
      this.$confirm('确定要清空所有日志吗？此操作不可恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('清空成功')
        this.tableData = []
        this.pagination.total = 0
      })
    },
    handleDetail(row) {
      this.currentLog = row
      this.detailDialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该日志吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadData()
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },
    loadData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
      }, 500)
    }
  },
  mounted() {
    this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.log-management {
  .search-area {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  pre {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
  }
}
</style>
