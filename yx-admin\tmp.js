import router from '.'
import store from '@/store'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { tokenKey } from '@/utils/constantKey'
import { sessionGet } from '@/utils/storage'
import { getUserMenuList } from '@/api/systemManage'
import { arrayToTree } from '@/utils/util'
import { filterAsyncRouter } from '@/store/modules/permission'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login']

router.beforeEach(async(to, from, next) => {
  document.title = to.meta.title || '红果研小程序管理平台'
  NProgress.start()
  const token = sessionGet(tokenKey)
  if (!token) {
    if (whiteList.includes(to.path)) {
      next()
      NProgress.done()
      return
    }
    next(`/login?redirect=${to.path}`)
    NProgress.done()
    return
  }

  if (to.path === '/login') {
    next({ path: '/' })
    NProgress.done()
    return
  }

  if (store.getters.user) {
    if (store.state.user.loadMenus) {
      next()
      return
    }
    store.dispatch('user/updateLoadMenus', true)
    loadMenus(next, to)
    return
  }

  store.dispatch('user/getUserInfo').then(() => {
    loadMenus(next, to)
  }).catch(() => {
    store.dispatch('user/logout').then(() => {
      location.reload()
    })
  })
})

export const loadMenus = (next, to) => {
  getUserMenuList().then(data => {
    const routeArr = arrayToTree(data)
    const asyncRoutes = filterAsyncRouter(routeArr)
    store.dispatch('permission/generateRoutes', asyncRoutes)
    router.addRoutes(asyncRoutes)
    next({ ...to, replace: true })
  })
}

router.afterEach(() => {
  NProgress.done()
})
