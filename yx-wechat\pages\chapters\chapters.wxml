<!--pages/chapters/chapters.wxml-->
<view class="container">
  <!-- 章节列表 -->
  <view class="chapters-list" wx:if="{{ chapters.length > 0 }}">
    <view 
      class="chapter-item" 
      wx:for="{{ chapters }}" 
      wx:key="id"
      bind:tap="goToQuestions"
      data-chapter="{{ item }}"
    >
      <view class="chapter-icon">
        <van-icon name="bookmark-o" size="32rpx" color="#1989fa" />
      </view>
      <view class="chapter-info">
        <view class="chapter-name">{{ item.name }}</view>
        <view class="chapter-progress">
          <van-progress 
            percentage="{{ item.progress || 0 }}" 
            stroke-width="6rpx"
            color="#1989fa"
            track-color="#ebedf0"
          />
          <text class="progress-text">{{ item.progress || 0 }}%</text>
        </view>
        <view class="chapter-stats">
          <text class="stat-text">{{ item.questionCount || 0 }}道题目</text>
          <text class="stat-text" wx:if="{{ item.completedCount }}">
            已完成 {{ item.completedCount }}道
          </text>
        </view>
      </view>
      <van-icon name="arrow" color="#c8c9cc" />
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <van-icon name="bookmark-o" size="80rpx" color="#c8c9cc" />
    <view class="empty-text">该书籍暂无章节</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <van-loading size="24px" />
    <view class="loading-text">加载中...</view>
  </view>
</view>

<van-toast id="van-toast" />
