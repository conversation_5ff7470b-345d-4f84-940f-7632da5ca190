const Router = require('koa-router');
const { response } = require('../utils/response');
const { hashPassword } = require('../utils/crypto');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取用户列表
router.get('/', permissionMiddleware('system:user'), async (ctx) => {
  const { page = 1, pageSize = 10, username, email, status } = ctx.query;
  
  let whereClause = 'WHERE 1=1';
  const params = [];
  
  if (username) {
    whereClause += ' AND username LIKE ?';
    params.push(`%${username}%`);
  }
  
  if (email) {
    whereClause += ' AND email LIKE ?';
    params.push(`%${email}%`);
  }
  
  if (status !== undefined) {
    whereClause += ' AND status = ?';
    params.push(status);
  }
  
  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;
  
  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT id, username, email, avatar, phone, status, created_time, updated_time, last_login_time
    FROM users 
    ${whereClause}
    ORDER BY created_time DESC
    LIMIT ? OFFSET ?
  `;
  params.push(parseInt(pageSize), parseInt(offset));
  
  const users = await query(dataSql, params);
  
  // 查询每个用户的角色
  for (let user of users) {
    const roles = await query(`
      SELECT r.id, r.name, r.code
      FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ? AND r.status = 1
    `, [user.id]);
    user.roles = roles;
  }
  
  ctx.body = response.page(users, total, page, pageSize);
});

// 获取用户详情
router.get('/:id', permissionMiddleware('system:user'), async (ctx) => {
  const { id } = ctx.params;
  
  const users = await query(
    'SELECT id, username, email, avatar, phone, status, created_time, updated_time, last_login_time FROM users WHERE id = ?',
    [id]
  );
  
  if (users.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('用户不存在', 404);
    return;
  }
  
  const user = users[0];
  
  // 查询用户角色
  const roles = await query(`
    SELECT r.id, r.name, r.code
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ? AND r.status = 1
  `, [user.id]);
  
  user.roles = roles;
  
  ctx.body = response.success(user);
});

// 创建用户
router.post('/', permissionMiddleware('system:user'), async (ctx) => {
  const { username, email, password, phone, roleIds = [] } = ctx.request.body;
  
  // 参数验证
  validation.required(username, '用户名');
  validation.required(email, '邮箱');
  validation.required(password, '密码');
  
  if (!validation.isUsername(username)) {
    ctx.status = 400;
    ctx.body = response.error('用户名格式不正确（3-20位字母数字下划线）', 400);
    return;
  }
  
  if (!validation.isEmail(email)) {
    ctx.status = 400;
    ctx.body = response.error('邮箱格式不正确', 400);
    return;
  }
  
  if (!validation.isStrongPassword(password)) {
    ctx.status = 400;
    ctx.body = response.error('密码强度不够（至少6位，包含字母和数字）', 400);
    return;
  }
  
  if (phone && !validation.isPhone(phone)) {
    ctx.status = 400;
    ctx.body = response.error('手机号格式不正确', 400);
    return;
  }
  
  // 检查用户名和邮箱是否已存在
  const existingUsers = await query(
    'SELECT id FROM users WHERE username = ? OR email = ?',
    [username, email]
  );
  
  if (existingUsers.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('用户名或邮箱已存在', 400);
    return;
  }
  
  // 加密密码
  const hashedPassword = await hashPassword(password);
  
  // 创建用户
  const result = await query(
    'INSERT INTO users (username, email, password, phone, status, created_time) VALUES (?, ?, ?, ?, ?, ?)',
    [username, email, hashedPassword, phone, 1, new Date()]
  );
  
  const userId = result.insertId;
  
  // 分配角色
  if (roleIds.length > 0) {
    const roleValues = roleIds.map(roleId => [userId, roleId, new Date()]);
    await query(
      'INSERT INTO user_roles (user_id, role_id, created_time) VALUES ?',
      [roleValues]
    );
  }
  
  ctx.body = response.success({ id: userId, username, email }, '用户创建成功');
});

module.exports = router;
