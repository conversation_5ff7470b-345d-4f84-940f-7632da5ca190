const Router = require('koa-router');
const { response } = require('../utils/response');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取角色列表
router.get('/', permissionMiddleware('system:role'), async (ctx) => {
  const { page = 1, pageSize = 10, name, status } = ctx.query;
  
  let whereClause = 'WHERE 1=1';
  const params = [];
  
  if (name) {
    whereClause += ' AND name LIKE ?';
    params.push(`%${name}%`);
  }
  
  if (status !== undefined) {
    whereClause += ' AND status = ?';
    params.push(status);
  }
  
  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM roles ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;
  
  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT id, name, code, description, status, created_time, updated_time
    FROM roles 
    ${whereClause}
    ORDER BY created_time DESC
    LIMIT ? OFFSET ?
  `;
  params.push(parseInt(pageSize), parseInt(offset));
  
  const roles = await query(dataSql, params);
  
  ctx.body = response.page(roles, total, page, pageSize);
});

// 获取所有启用的角色（不分页）
router.get('/all', async (ctx) => {
  const roles = await query(
    'SELECT id, name, code, description FROM roles WHERE status = 1 ORDER BY created_time DESC'
  );
  
  ctx.body = response.success(roles);
});

// 获取角色详情
router.get('/:id', permissionMiddleware('system:role'), async (ctx) => {
  const { id } = ctx.params;
  
  const roles = await query(
    'SELECT id, name, code, description, status, created_time, updated_time FROM roles WHERE id = ?',
    [id]
  );
  
  if (roles.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('角色不存在', 404);
    return;
  }
  
  const role = roles[0];
  
  // 查询角色权限
  const permissions = await query(`
    SELECT p.id, p.name, p.code, p.type
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    WHERE rp.role_id = ? AND p.status = 1
  `, [role.id]);
  
  role.permissions = permissions;
  
  ctx.body = response.success(role);
});

// 创建角色
router.post('/', permissionMiddleware('system:role'), async (ctx) => {
  const { name, code, description, permissionIds = [] } = ctx.request.body;
  
  // 参数验证
  validation.required(name, '角色名称');
  validation.required(code, '角色编码');
  validation.length(name, 1, 50, '角色名称');
  validation.length(code, 1, 50, '角色编码');
  
  // 检查编码是否已存在
  const existingRoles = await query(
    'SELECT id FROM roles WHERE code = ?',
    [code]
  );
  
  if (existingRoles.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('角色编码已存在', 400);
    return;
  }
  
  // 创建角色
  const result = await query(
    'INSERT INTO roles (name, code, description, status, created_time) VALUES (?, ?, ?, ?, ?)',
    [name, code, description, 1, new Date()]
  );
  
  const roleId = result.insertId;
  
  // 分配权限
  if (permissionIds.length > 0) {
    const permissionValues = permissionIds.map(permissionId => [roleId, permissionId, new Date()]);
    await query(
      'INSERT INTO role_permissions (role_id, permission_id, created_time) VALUES ?',
      [permissionValues]
    );
  }
  
  ctx.body = response.success({ id: roleId, name, code }, '角色创建成功');
});

// 更新角色
router.put('/:id', permissionMiddleware('system:role'), async (ctx) => {
  const { id } = ctx.params;
  const { name, code, description, status, permissionIds = [] } = ctx.request.body;
  
  // 参数验证
  validation.required(name, '角色名称');
  validation.required(code, '角色编码');
  validation.length(name, 1, 50, '角色名称');
  validation.length(code, 1, 50, '角色编码');
  
  // 检查角色是否存在
  const existingRoles = await query(
    'SELECT id FROM roles WHERE id = ?',
    [id]
  );
  
  if (existingRoles.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('角色不存在', 404);
    return;
  }
  
  // 检查编码是否被其他角色使用
  const duplicateRoles = await query(
    'SELECT id FROM roles WHERE code = ? AND id != ?',
    [code, id]
  );
  
  if (duplicateRoles.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('角色编码已被其他角色使用', 400);
    return;
  }
  
  // 更新角色
  await query(
    'UPDATE roles SET name = ?, code = ?, description = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, code, description, status, new Date(), id]
  );
  
  // 更新权限关联
  await query('DELETE FROM role_permissions WHERE role_id = ?', [id]);
  
  if (permissionIds.length > 0) {
    const permissionValues = permissionIds.map(permissionId => [id, permissionId, new Date()]);
    await query(
      'INSERT INTO role_permissions (role_id, permission_id, created_time) VALUES ?',
      [permissionValues]
    );
  }
  
  ctx.body = response.success(null, '角色更新成功');
});

// 删除角色
router.delete('/:id', permissionMiddleware('system:role'), async (ctx) => {
  const { id } = ctx.params;
  
  // 检查角色是否存在
  const existingRoles = await query(
    'SELECT id FROM roles WHERE id = ?',
    [id]
  );
  
  if (existingRoles.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('角色不存在', 404);
    return;
  }
  
  // 检查是否有用户使用该角色
  const userRoles = await query(
    'SELECT id FROM user_roles WHERE role_id = ?',
    [id]
  );
  
  if (userRoles.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('该角色下存在用户，无法删除', 400);
    return;
  }
  
  // 删除角色权限关联
  await query('DELETE FROM role_permissions WHERE role_id = ?', [id]);
  
  // 删除角色
  await query('DELETE FROM roles WHERE id = ?', [id]);
  
  ctx.body = response.success(null, '角色删除成功');
});

module.exports = router;
