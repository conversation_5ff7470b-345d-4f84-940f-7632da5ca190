/* pages/question-banks/question-banks.wxss */
.container {
  padding: 24rpx;
}

/* 题库列表 */
.banks-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.bank-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebedf0;
  transition: background-color 0.2s;
}

.bank-item:last-child {
  border-bottom: none;
}

.bank-item:active {
  background-color: #f7f8fa;
}

.bank-info {
  flex: 1;
  margin-right: 16rpx;
}

.bank-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.bank-desc {
  font-size: 28rpx;
  color: #969799;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.bank-stats {
  display: flex;
  gap: 16rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 24rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 16rpx;
}
