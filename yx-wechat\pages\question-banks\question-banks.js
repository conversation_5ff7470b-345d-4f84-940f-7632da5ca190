// pages/question-banks/question-banks.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')
import Toast from '@vant/weapp/toast/toast'

Page({
  data: {
    subjectId: '',
    subjectName: '',
    questionBanks: [],
    loading: false
  },

  onLoad(options) {
    const { subjectId, subjectName } = options
    
    if (!subjectId) {
      Toast.fail('参数错误')
      wx.navigateBack()
      return
    }

    this.setData({
      subjectId,
      subjectName: decodeURIComponent(subjectName || '')
    })

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.subjectName || '题库'
    })

    this.checkLoginAndLoadData()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    if (!auth.requireLogin()) return
    this.loadQuestionBanks()
  },

  // 加载题库列表
  loadQuestionBanks() {
    this.setData({ loading: true })
    
    api.questionBanks.list({ subjectId: this.data.subjectId })
      .then(res => {
        const questionBanks = res.data?.list || []
        this.setData({ questionBanks })
      })
      .catch(err => {
        console.error('加载题库失败:', err)
        Toast.fail('加载题库失败')
      })
      .finally(() => {
        this.setData({ loading: false })
      })
  },

  // 跳转到书籍页面
  goToBooks(e) {
    const bank = e.currentTarget.dataset.bank
    wx.navigateTo({
      url: `/pages/books/books?subjectId=${this.data.subjectId}&subjectName=${this.data.subjectName}&bankId=${bank.id}&bankName=${bank.name}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadQuestionBanks()
    wx.stopPullDownRefresh()
  }
})
