# YX刷题项目

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D14.0.0-brightgreen)](https://nodejs.org/)
[![Vue.js Version](https://img.shields.io/badge/vue-2.6.14-green)](https://vuejs.org/)
[![Koa Version](https://img.shields.io/badge/koa-2.14.2-blue)](https://koajs.com/)

YX刷题项目是一个完整的在线学习平台，包含后台管理系统、API接口服务和微信小程序三个核心模块。

## 📖 项目概述

本项目为教育培训机构和在线学习平台提供完整的题库管理和在线练习解决方案，支持多学科、多层级的题目组织结构。

### 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   yx-wechat     │    │    yx-admin     │    │     yx-api      │
│  微信小程序端    │◄──►│   后台管理系统   │◄──►│   后端API接口   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │   MySQL数据库   │
                                              │                 │
                                              └─────────────────┘
```

### 题库层级结构
```
学科 (Subject)
└── 题库 (Question Bank)
    └── 书籍 (Book)
        └── 章 (Chapter)
            └── 节 (Section)
                └── 题目 (Question)
```

### 📁 项目结构

```
yx-shuati/
├── yx-admin/           # Vue.js 后台管理系统
├── yx-api/             # Koa.js 后端API服务
├── yx-wechat/          # 微信小程序
├── .gitignore          # Git忽略规则
├── LICENSE             # MIT开源许可证
├── setup.sh            # Linux/Mac安装脚本
├── setup.bat           # Windows安装脚本
└── README.md           # 项目完整说明文档
```

## ✨ 核心特性

### 🎯 分层级题库管理
支持"学科-题库-书籍-章-节"五级结构，灵活组织题目内容

### 🔐 完整权限体系
基于角色的权限管理(RBAC)，精细化控制用户权限

### 📊 数据可视化
直观的统计图表和数据展示，实时监控学习进度

### 📱 多端支持
- **Web端**: 基于Vue.js的现代化管理后台
- **移动端**: 微信小程序原生开发，流畅的用户体验

### 🔍 智能功能
- 多条件组合搜索和筛选
- 实时学习进度跟踪
- 个性化学习建议

## 🚀 快速开始

### 环境要求

- **Node.js** >= 14.0.0
- **MySQL** >= 5.7
- **npm** >= 6.0.0
- **微信开发者工具** (用于小程序开发)

### 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/lanchaguo/yx-shuati.git
cd yx-shuati
```

#### 2. 启动后端API服务
```bash
cd yx-api
npm install
node scripts/initDatabase.js
npm run dev
```
服务将在 http://localhost:3000 启动

#### 3. 启动后台管理系统
```bash
cd ../yx-admin
npm install
npm run serve
```
管理后台将在 http://localhost:8080 启动

#### 4. 启动微信小程序
```bash
cd ../yx-wechat
npm install
```
然后使用微信开发者工具导入项目，执行"构建npm"操作

### 默认账号

- **用户名**: admin
- **密码**: 123456

## 🛠️ 技术栈

### yx-admin (后台管理)
- **前端框架**: Vue 2.6.14
- **UI组件库**: Element UI 2.15.14
- **路由管理**: Vue Router 3.5.4
- **状态管理**: Vuex 3.6.2
- **HTTP客户端**: Axios 1.6.0

### yx-api (后端服务)
- **框架**: Koa 2.14.2
- **数据库**: MySQL
- **认证**: JWT
- **密码加密**: bcryptjs

### yx-wechat (微信小程序)
- **开发框架**: 微信小程序原生
- **UI组件库**: Vant Weapp
- **网络请求**: 封装的wx.request

## 📚 功能模块

### 系统管理
- ✅ 用户管理 - 用户增删改查、角色分配
- ✅ 角色管理 - 角色权限配置
- ✅ 菜单管理 - 动态菜单配置
- ✅ 权限管理 - 细粒度权限控制
- ✅ 系统日志 - 操作记录追踪
- ✅ 学科管理 - 学科分类管理

### 题库管理
- ✅ 题库管理 - 题库创建和维护
- ✅ 书籍管理 - 教材版本管理
- ✅ 章节管理 - 层级结构管理
- ✅ 题目管理 - 多题型支持
- ✅ 分类管理 - 题目分类体系

### 学习功能
- ✅ 在线练习 - 多种题型练习
- ✅ 进度跟踪 - 学习进度记录
- ✅ 统计分析 - 学习数据分析
- ✅ 错题管理 - 错题收集整理

## 🗄️ 数据库配置

```
地址：101.43.125.38
数据库名：yx-db
用户名：yx-db
密码：yDtxSFsdNMHTRbJw
```

## 🔐 默认账号信息

所有子项目统一使用以下默认管理员账号：
- **用户名**: admin
- **密码**: 123456

## 🔧 开发指南

### 代码规范
项目遵循统一的代码规范，详见各子项目的开发文档。

### Git提交规范
```bash
feat: 新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
perf: 性能优化
test: 测试相关
```

### 分支管理
- `main` - 主分支，用于生产环境
- `develop` - 开发分支，用于功能集成
- `feature/*` - 功能分支，用于新功能开发
- `hotfix/*` - 热修复分支，用于紧急修复

## 🚧 开发状态

| 模块 | 状态 | 进度 |
|------|------|------|
| yx-admin | ✅ 完成 | 100% |
| yx-api | 🚧 开发中 | 80% |
| yx-wechat | ✅ 完成 | 100% |

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址: https://github.com/lanchaguo/yx-shuati.git
- 问题反馈: [GitHub Issues](https://github.com/lanchaguo/yx-shuati/issues)
- 邮箱: [<EMAIL>]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## 📚 核心功能模块

### 1. 系统管理模块
- **用户管理**: 用户增删改查、状态管理、角色分配、批量操作
- **角色管理**: 角色增删改查、权限分配、角色状态管理
- **菜单管理**: 树形菜单结构、菜单图标配置、路由管理、按钮权限控制
- **权限管理**: 菜单权限、按钮权限、接口权限、权限分类管理
- **系统日志**: 操作日志记录、登录日志、日志搜索筛选、日志详情查看
- **学科管理**: 学科增删改查、学科图标和主题色、学科排序

### 2. 题库管理模块
- **题库管理**: 题库增删改查、学科关联、统计信息显示
- **书籍管理**: 书籍增删改查、年级分类、版本管理、章节统计
- **章节管理**: 树形章节结构、章/节分级管理、拖拽排序、题目统计
- **题目分类管理**: 分类增删改查、层级管理、统计信息
- **题目管理**: 题目增删改查、多种题型支持、批量导入、答案解析

### 3. 微信小程序功能
- **用户认证**: 用户登录/注册、自动登录状态保持、安全的JWT认证
- **学习功能**: 学科浏览、题库导航、题目练习、即时反馈、进度跟踪
- **学习统计**: 总体学习数据统计、正确率分析、最近练习记录
- **个人中心**: 用户信息管理、学习数据查看、缓存管理、系统设置

## 📋 API接口文档

### 基础信息
- 基础URL: `http://localhost:3000/api`
- 认证方式: Bearer Token (JWT)
- 响应格式: JSON

### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 主要接口

#### 认证接口
- **POST** `/api/auth/login` - 用户登录
- **POST** `/api/auth/register` - 用户注册
- **GET** `/api/auth/profile` - 获取用户信息

#### 系统管理
- **GET** `/api/users` - 获取用户列表
- **GET** `/api/roles` - 获取角色列表
- **GET** `/api/permissions` - 获取权限列表
- **GET** `/api/subjects` - 获取学科列表

#### 题库管理
- **GET** `/api/question-banks` - 获取题库列表
- **GET** `/api/books` - 获取书籍列表
- **GET** `/api/chapters` - 获取章节列表
- **GET** `/api/questions` - 获取题目列表

## 🗄️ 数据库表结构

### 用户权限相关
- `users` - 用户表
- `roles` - 角色表
- `permissions` - 权限表
- `user_roles` - 用户角色关联表
- `role_permissions` - 角色权限关联表
- `menus` - 菜单表

### 日志相关
- `system_logs` - 系统日志表
- `login_logs` - 登录日志表

### 题库相关
- `subjects` - 学科表
- `question_banks` - 题库表
- `books` - 书籍表
- `chapters` - 章节表
- `question_categories` - 题目分类表
- `questions` - 题目表
- `question_options` - 题目选项表

## 🎯 使用流程

### 管理员操作流程
1. **学科配置**: 在系统管理中配置学科
2. **题库创建**: 在题库管理中创建题库并关联学科
3. **书籍添加**: 为题库添加不同年级的书籍
4. **章节构建**: 为书籍创建章节结构
5. **分类设置**: 配置题目分类和类型
6. **题目录入**: 录入具体题目

### 用户学习流程
1. **登录注册**: 使用微信小程序登录
2. **选择学科**: 浏览可用学科
3. **选择题库**: 进入感兴趣的题库
4. **选择书籍**: 选择对应年级的书籍
5. **选择章节**: 选择要练习的章节
6. **开始练习**: 进行题目练习
7. **查看统计**: 查看学习进度和统计

## 🚧 开发状态

### yx-admin (后台管理)
- ✅ 用户管理
- ✅ 角色管理
- ✅ 菜单管理
- ✅ 权限管理
- ✅ 系统日志
- ✅ 学科管理
- ✅ 题库管理
- ✅ 书籍管理
- ✅ 章节管理
- ✅ 题目分类管理
- ✅ 题目管理

### yx-api (后端API)
- ✅ 项目基础架构
- ✅ 用户认证系统
- ✅ 用户管理
- ✅ 角色管理
- ✅ 权限管理
- ✅ 学科管理
- ✅ 题库管理
- 🚧 书籍管理（待完善）
- 🚧 章节管理（待完善）
- 🚧 题目分类管理（待完善）
- 🚧 题目管理（待完善）
- 🚧 系统日志（待完善）

### yx-wechat (微信小程序)
- ✅ 用户登录/注册功能
- ✅ 学科浏览和题库导航
- ✅ 多种题型的在线练习功能
- ✅ 学习进度跟踪和统计
- ✅ 个人中心和用户信息管理
- ✅ 本地数据存储和缓存管理

## 📊 更新日志

### [1.0.0] - 2024-01-01

#### ✨ 新增功能

##### 项目整合
- 🔧 将三个子项目整合到统一的Git仓库
- 📋 创建统一的项目需求文档整合到README.md
- 🗑️ 删除子项目中的重复MD文档，避免重复维护
- 🚫 配置完整的 `.gitignore` 文件
- 📄 添加 MIT 许可证文件
- 🔧 添加自动化安装脚本 (setup.sh/setup.bat)

##### yx-admin (后台管理系统)
- 🔐 完整的用户权限管理系统
- 👥 用户管理 - 增删改查、角色分配、批量操作
- 🎭 角色管理 - 权限分配、状态管理
- 📋 菜单管理 - 树形结构、图标配置、路由管理
- 🔑 权限管理 - 菜单权限、按钮权限、接口权限
- 📊 系统日志 - 操作记录、登录日志、搜索筛选
- 📚 学科管理 - 学科分类、图标主题、排序功能
- 📖 题库管理 - 题库增删改查、学科关联、统计信息
- 📕 书籍管理 - 年级分类、版本管理、章节统计
- 📑 章节管理 - 树形结构、拖拽排序、题目统计
- 🏷️ 题目分类管理 - 分类体系、层级管理
- ❓ 题目管理 - 多题型支持、批量导入、答案解析

##### yx-api (后端API服务)
- 🏗️ 基于 Koa3 + MySQL 的后端架构
- 🔐 JWT 认证系统和权限验证中间件
- 👤 用户认证 - 登录注册、密码加密、令牌管理
- 🗄️ 完整的数据库表结构设计
- 📊 RBAC 权限体系 - 用户、角色、权限关联
- 🎯 题库层级结构 - 学科→题库→书籍→章→节→题目
- 🔧 统一的错误处理和响应格式
- 📝 数据验证和输入校验
- 🚀 数据库初始化脚本和默认数据

##### yx-wechat (微信小程序)
- 📱 微信小程序原生开发
- 🎨 基于 Vant Weapp 的美观界面
- 🔐 用户登录注册和自动登录保持
- 📚 学科浏览和题库导航功能
- 📝 多种题型的在线练习
- ⚡ 即时反馈和答案解析
- 📈 学习进度跟踪和统计分析
- 👤 个人中心和用户信息管理
- 💾 本地数据存储和缓存管理

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
